const https = require('https');

class AIService {
  constructor(apiKey, database = null) {
    this.apiKey = apiKey;
    this.baseURL = 'https://openrouter.ai/api/v1';
    this.database = database;
    this.requestCounter = 0;
  }

  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  getCurrentSystemPrompt(type = null) {
    if (this.database) {
      const defaultPrompt = this.database.getDefaultSystemPrompt();
      if (!defaultPrompt) return null;
      
      if (type && defaultPrompt.prompts && defaultPrompt.prompts[type]) {
        return defaultPrompt.prompts[type];
      }
      
      // Fallback to first available prompt type or return null
      if (defaultPrompt.prompts) {
        const firstType = Object.keys(defaultPrompt.prompts)[0];
        return firstType ? defaultPrompt.prompts[firstType] : null;
      }
      
      return null;
    }
    return null;
  }

  logRequest(requestId, method, url, headers, data, promptType = 'general') {
    console.log(`\n🚀 [REQUEST ${requestId}] AI API Request Started`);
    console.log(`   📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`   🔗 URL: ${url}`);
    console.log(`   📝 Method: ${method}`);
    console.log(`   🏷️  Prompt Type: ${promptType}`);
    console.log(`   📊 Headers:`, JSON.stringify({ ...headers, 'Authorization': 'Bearer [REDACTED]' }, null, 2));
    console.log(`   📦 Request Data:`, JSON.stringify(data, null, 2));
    console.log(`   📏 Data Size: ${JSON.stringify(data).length} characters`);
    
    // Additional debugging for potential issues
    if (data.messages) {
      console.log(`   📋 Message Analysis:`);
      console.log(`      📝 Total messages: ${data.messages.length}`);
      data.messages.forEach((msg, index) => {
        console.log(`      ${index + 1}. Role: ${msg.role}, Content length: ${msg.content ? msg.content.length : 0} chars`);
        if (msg.content && msg.content.length > 500) {
          console.log(`         ⚠️  Long message (${msg.content.length} chars) - may hit token limits`);
        }
      });
    }
    
    if (data.model) {
      console.log(`   🤖 Model: ${data.model}`);
    }
    
    if (data.max_tokens) {
      console.log(`   🔢 Max tokens: ${data.max_tokens}`);
    }
  }

  logResponse(requestId, statusCode, statusMessage, responseData, isStreaming = false) {
    console.log(`\n📡 [RESPONSE ${requestId}] AI API Response Received`);
    console.log(`   📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`   ✅ Status: ${statusCode} ${statusMessage}`);
    console.log(`   🔄 Streaming: ${isStreaming}`);
    
    if (!isStreaming && responseData) {
      try {
        const parsed = JSON.parse(responseData);
        console.log(`   📄 Full Response:`, JSON.stringify(parsed, null, 2));
        
        if (parsed.choices && parsed.choices[0]) {
          const choice = parsed.choices[0];
          console.log(`   💬 AI Response Content: "${choice.message?.content || 'NO CONTENT'}"`);
          
          // Check for finish reason which might explain empty responses
          if (choice.finish_reason) {
            console.log(`   🏁 Finish Reason: ${choice.finish_reason}`);
            if (choice.finish_reason === 'length') {
              console.log(`   ⚠️  Response was cut off due to token limit`);
            } else if (choice.finish_reason === 'content_filter') {
              console.log(`   🚫 Response was filtered due to content policy`);
            } else if (choice.finish_reason === 'stop') {
              console.log(`   ✅ Response completed normally`);
            } else if (choice.finish_reason === 'tool_calls') {
              console.log(`   🔧 Response was tool calls (not text content)`);
            }
          }
          
          // Check if response is empty
          if (!choice.message?.content || choice.message.content.trim() === '') {
            console.log(`   ⚠️  EMPTY RESPONSE DETECTED`);
            console.log(`   🔍 Possible causes:`);
            console.log(`      - Model refused to respond to the prompt`);
            console.log(`      - Content filtering removed all content`);
            console.log(`      - Token limit reached before any content generated`);
            console.log(`      - Model-specific issue or bug`);
            console.log(`      - Invalid prompt format or content`);
          }
        }
        
        if (parsed.usage) {
          console.log(`   📊 Usage:`, JSON.stringify(parsed.usage, null, 2));
          
          // Check if we're hitting token limits
          if (parsed.usage.total_tokens > 3000) {
            console.log(`   ⚠️  High token usage (${parsed.usage.total_tokens}) - may be hitting limits`);
          }
        }
        
        // Check for errors in the response
        if (parsed.error) {
          console.log(`   ❌ API Error in response:`, JSON.stringify(parsed.error, null, 2));
        }
        
      } catch (e) {
        console.log(`   ⚠️  Raw Response (unparseable): ${responseData}`);
        console.log(`   🔍 Parse Error: ${e.message}`);
      }
    }
  }

  logError(requestId, error, context = '') {
    console.log(`\n❌ [ERROR ${requestId}] AI API Error`);
    console.log(`   📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`   🔍 Context: ${context}`);
    console.log(`   💥 Error: ${error.message}`);
    console.log(`   📚 Stack: ${error.stack}`);
  }

  logStreamingChunk(requestId, chunk) {
    console.log(`   🔄 [STREAM ${requestId}] Chunk: "${chunk}"`);
  }

  logEmptyResponse(requestId, context = '') {
    console.log(`\n⚠️  [EMPTY_RESPONSE ${requestId}] AI returned empty response`);
    console.log(`   📅 Timestamp: ${new Date().toISOString()}`);
    console.log(`   🔍 Context: ${context}`);
    console.log(`   💡 Possible causes:`);
    console.log(`      - Model hit token limit`);
    console.log(`      - Content filtering triggered`);
    console.log(`      - Model refused to respond`);
    console.log(`      - Network interruption during streaming`);
    console.log(`      - Invalid prompt format`);
  }

  logStreamingData(requestId, rawData) {
    console.log(`   🔍 [STREAM_DEBUG ${requestId}] Raw streaming data: "${rawData}"`);
  }

  async sendMessage(message, model = 'deepseek/deepseek-chat-v3-0324"', onChunk = null, systemPrompt = null, promptType = null) {
    if (!this.apiKey) {
      throw new Error('API key not set');
    }

    const requestId = ++this.requestCounter;

    // Handle both string messages and message objects
    let messageContent = message;
    let messageRole = 'user';
    
    if (typeof message === 'object' && message.content) {
      messageContent = message.content;
      messageRole = message.role || 'user';
    }

    // Build messages array
    const messages = [];
    
    // Add system prompt if provided, otherwise get from database
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    } else {
      const currentSystemPrompt = this.getCurrentSystemPrompt(promptType);
      if (currentSystemPrompt) {
        messages.push({
          role: 'system',
          content: currentSystemPrompt
        });
      }
    }
    
    // Add user message
    messages.push({
      role: messageRole,
      content: messageContent
    });

    const requestData = {
      model: model,
      messages: messages,
      max_tokens: 1000,
      temperature: 0.7,
      stream: onChunk ? true : false
    };

    const data = JSON.stringify(requestData);

    const options = {
      hostname: 'openrouter.ai',
      port: 443,
      path: '/api/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': 'https://ai-chat-electron.app',
        'X-Title': 'AI Chat Electron'
      }
    };

    // Log the complete request
    this.logRequest(requestId, options.method, `https://${options.hostname}${options.path}`, options.headers, requestData, promptType || 'chat');

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        if (onChunk && res.statusCode === 200) {
          // Handle streaming response
          let fullResponse = '';
          
          res.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  console.log(`\n✅ [STREAM ${requestId}] Streaming completed`);
                  console.log(`   📄 Full streamed response: "${fullResponse}"`);
                  resolve('');
                  return;
                }
                try {
                  const parsed = JSON.parse(data);
                  const content = parsed.choices[0]?.delta?.content;
                  if (content) {
                    fullResponse += content;
                    this.logStreamingChunk(requestId, content);
                    onChunk(content);
                  }
                } catch (e) {
                  // Ignore parsing errors for incomplete chunks
                }
              }
            }
          });
          
          res.on('end', () => {
            this.logResponse(requestId, res.statusCode, res.statusMessage, null, true);
            resolve('');
          });
        } else {
          // Handle non-streaming response
          let responseData = '';

          res.on('data', (chunk) => {
            responseData += chunk;
          });

          res.on('end', () => {
            this.logResponse(requestId, res.statusCode, res.statusMessage, responseData, false);
            
            try {
              const parsedData = JSON.parse(responseData);
              
              if (res.statusCode === 200) {
                const aiResponse = parsedData.choices[0]?.message?.content;
                if (aiResponse) {
                  console.log(`\n✅ [REQUEST ${requestId}] Successfully completed`);
                  resolve(aiResponse);
                } else {
                  const error = new Error('Invalid response format from AI');
                  this.logError(requestId, error, 'Missing AI response content');
                  reject(error);
                }
              } else {
                const error = new Error(`API Error: ${parsedData.error?.message || 'Unknown error'}`);
                this.logError(requestId, error, `HTTP ${res.statusCode}`);
                reject(error);
              }
            } catch (error) {
              this.logError(requestId, error, 'Response parsing failed');
              reject(new Error(`Failed to parse response: ${error.message}`));
            }
          });
        }
      });

      req.on('error', (error) => {
        this.logError(requestId, error, 'Network request failed');
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.write(data);
      req.end();
    });
  }

  async testConnection() {
    const requestId = ++this.requestCounter;
    
    try {
      // Make a minimal test request that validates the API key quickly
      const requestData = {
        model: 'deepseek/deepseek-chat-v3-0324"',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 10, // Very short response
        temperature: 0.7
      };

      const data = JSON.stringify(requestData);

      const options = {
        hostname: 'openrouter.ai',
        port: 443,
        path: '/api/v1/chat/completions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': data.length,
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': 'https://ai-chat-electron.app',
          'X-Title': 'AI Chat Electron'
        }
      };

      // Log the test request
      this.logRequest(requestId, options.method, `https://${options.hostname}${options.path}`, options.headers, requestData, 'connection_test');

      return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
          let responseData = '';

          res.on('data', (chunk) => {
            responseData += chunk;
          });

          res.on('end', () => {
            this.logResponse(requestId, res.statusCode, res.statusMessage, responseData, false);
            
            try {
              const parsedData = JSON.parse(responseData);
              
              if (res.statusCode === 200) {
                // API key is valid, we don't need the full response
                console.log(`\n✅ [REQUEST ${requestId}] Connection test successful`);
                resolve({ success: true, response: 'API key validated successfully' });
              } else {
                const error = new Error(parsedData.error?.message || 'Invalid API key');
                this.logError(requestId, error, 'Connection test failed');
                resolve({ success: false, error: error.message });
              }
            } catch (error) {
              this.logError(requestId, error, 'Connection test response parsing failed');
              resolve({ success: false, error: 'Failed to parse response' });
            }
          });
        });

        req.on('error', (error) => {
          this.logError(requestId, error, 'Connection test network error');
          resolve({ success: false, error: `Request failed: ${error.message}` });
        });

        // Set a timeout to prevent hanging
        req.setTimeout(10000, () => {
          req.destroy();
          const error = new Error('Request timeout');
          this.logError(requestId, error, 'Connection test timeout');
          resolve({ success: false, error: 'Request timeout' });
        });

        req.write(data);
        req.end();
      });
    } catch (error) {
      this.logError(requestId, error, 'Connection test exception');
      return { success: false, error: error.message };
    }
  }

  async enhanceCharacterText(text, field, characterName, imageBase64 = null, onChunk = null) {
    const requestId = ++this.requestCounter;
    
    console.log(`\n🎭 [CHARACTER_ENHANCE ${requestId}] Starting character text enhancement`);
    console.log(`   👤 Character: ${characterName}`);
    console.log(`   📝 Field: ${field}`);
    console.log(`   📄 Original text: "${text}"`);
    console.log(`   🖼️  Has image: ${imageBase64 ? 'Yes' : 'No'}`);
    
    const imageContext = imageBase64 ? "I've also provided an image of this character for visual reference. Please use the image to inform your enhancement and make the description more accurate and detailed based on what you can see." : "";
    
    const prompts = {
      personality: `Enhance this character personality description to be more detailed, engaging, and fitting to the system prompt above. Make it more vivid and add depth while maintaining the original essence, as well as a brief backstory for the character for how their personality developed. Do not pay too much attention to characters facial expressions of clothes in description.

Original: "${text}"

Character Name: ${characterName}

${imageContext}

IMPORTANT: Output ONLY the enhanced personality description. Do not include any explanations, introductions, or additional text. Just provide the enhanced personality description directly.

Enhanced personality:`,
      appearance: `Enhance this character appearance description to be more detailed, vivid and fitting to the system prompt above. Make it more descriptive and visually appealing while maintaining the original essence.

Original: "${text}"

Character Name: ${characterName}

${imageContext}

IMPORTANT: Output ONLY the enhanced appearance description. Do not include any explanations, introductions, or additional text. Just provide the enhanced appearance description directly.

Enhanced appearance:`
    };

    const prompt = prompts[field] || prompts.personality;
    
    console.log(`\n📝 [CHARACTER_ENHANCE ${requestId}] Generated prompt:`);
    console.log(`   ${prompt}`);
    
    try {
      // If image is provided, use vision model with image context
      if (imageBase64) {
        console.log(`\n🖼️  [CHARACTER_ENHANCE ${requestId}] Using vision model with image`);
        return await this.sendMessageWithImage(prompt, imageBase64, onChunk, null, 'character_generation');
      } else {
        console.log(`\n💬 [CHARACTER_ENHANCE ${requestId}] Using text-only model`);
        return await this.sendMessage(prompt, 'deepseek/deepseek-chat-v3-0324"', onChunk, null, 'character_generation');
      }
    } catch (error) {
      this.logError(requestId, error, `Character enhancement failed for ${field}`);
      throw new Error(`Failed to enhance ${field}: ${error.message}`);
    }
  }

  async sendMessageWithImage(message, imageBase64, onChunk = null, systemPrompt = null) {
    if (!this.apiKey) {
      throw new Error('API key not set');
    }

    const requestId = ++this.requestCounter;

    // Remove data URL prefix if present
    const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');

    console.log(`\n🖼️  [VISION ${requestId}] Processing image message`);
    console.log(`   📏 Image base64 length: ${base64Data.length} characters`);

    // Build messages array
    const messages = [];
    
    // Add system prompt if provided, otherwise get from database
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    } else {
      const currentSystemPrompt = this.getCurrentSystemPrompt();
      if (currentSystemPrompt) {
        messages.push({
          role: 'system',
          content: currentSystemPrompt
        });
      }
    }
    
    // Add user message with image
    messages.push({
      role: 'user',
      content: [
        {
          type: 'text',
          text: message
        },
        {
          type: 'image_url',
          image_url: {
            url: `data:image/jpeg;base64,${base64Data}`
          }
        }
      ]
    });

    const requestData = {
      model: 'google/gemini-2.0-flash-001',
      messages: messages,
      max_tokens: 1000,
      temperature: 0.7,
      stream: onChunk ? true : false
    };

    const data = JSON.stringify(requestData);

    const options = {
      hostname: 'openrouter.ai',
      port: 443,
      path: '/api/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Authorization': `Bearer ${this.apiKey}`,
        'HTTP-Referer': 'https://ai-chat-electron.app',
        'X-Title': 'AI Chat Electron'
      }
    };

    // Log the vision request
    this.logRequest(requestId, options.method, `https://${options.hostname}${options.path}`, options.headers, requestData, 'vision');

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        if (onChunk && res.statusCode === 200) {
          // Handle streaming response
          let fullResponse = '';
          let chunkCount = 0;
          let hasReceivedContent = false;
          
          res.on('data', (chunk) => {
            const chunkStr = chunk.toString();
            this.logStreamingData(requestId, chunkStr);
            
            const lines = chunkStr.split('\n');
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  console.log(`\n✅ [VISION_STREAM ${requestId}] Vision streaming completed`);
                  console.log(`   📄 Full streamed response: "${fullResponse}"`);
                  console.log(`   📊 Total chunks received: ${chunkCount}`);
                  console.log(`   📏 Response length: ${fullResponse.length} characters`);
                  
                  if (fullResponse.trim() === '') {
                    this.logEmptyResponse(requestId, 'Streaming completed but no content received');
                    reject(new Error('AI returned empty response during streaming'));
                  } else {
                    resolve(fullResponse);
                  }
                  return;
                }
                try {
                  const parsed = JSON.parse(data);
                  chunkCount++;
                  
                  // Log the parsed chunk for debugging
                  console.log(`   🔍 [STREAM_DEBUG ${requestId}] Parsed chunk:`, JSON.stringify(parsed, null, 2));
                  
                  const content = parsed.choices[0]?.delta?.content;
                  if (content) {
                    hasReceivedContent = true;
                    fullResponse += content;
                    this.logStreamingChunk(requestId, content);
                    onChunk(content);
                  } else {
                    console.log(`   ⚠️  [STREAM_DEBUG ${requestId}] Chunk has no content:`, JSON.stringify(parsed, null, 2));
                  }
                } catch (e) {
                  console.log(`   ⚠️  [STREAM_DEBUG ${requestId}] Failed to parse chunk: "${data}" - Error: ${e.message}`);
                  // Ignore parsing errors for incomplete chunks
                }
              }
            }
          });
          
          res.on('end', () => {
            this.logResponse(requestId, res.statusCode, res.statusMessage, null, true);
            
            // Check if we received any content during streaming
            if (!hasReceivedContent || fullResponse.trim() === '') {
              this.logEmptyResponse(requestId, 'Streaming ended without content');
              reject(new Error('AI returned empty response - no content received during streaming'));
            } else {
              resolve(fullResponse);
            }
          });
        } else {
          // Handle non-streaming response
          let responseData = '';

          res.on('data', (chunk) => {
            responseData += chunk;
          });

          res.on('end', () => {
            this.logResponse(requestId, res.statusCode, res.statusMessage, responseData, false);
            
            try {
              const parsedData = JSON.parse(responseData);
              
              if (res.statusCode === 200) {
                const aiResponse = parsedData.choices[0]?.message?.content;
                if (aiResponse) {
                  console.log(`\n✅ [VISION ${requestId}] Successfully completed`);
                  resolve(aiResponse);
                } else {
                  const error = new Error('Invalid response format from AI');
                  this.logError(requestId, error, 'Missing AI response content in vision request');
                  reject(error);
                }
              } else {
                const error = new Error(`API Error: ${parsedData.error?.message || 'Unknown error'}`);
                this.logError(requestId, error, `Vision request HTTP ${res.statusCode}`);
                reject(error);
              }
            } catch (error) {
              this.logError(requestId, error, 'Vision response parsing failed');
              reject(new Error(`Failed to parse response: ${error.message}`));
            }
          });
        }
      });

      req.on('error', (error) => {
        this.logError(requestId, error, 'Vision request network error');
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.write(data);
      req.end();
    });
  }
}

module.exports = AIService; 