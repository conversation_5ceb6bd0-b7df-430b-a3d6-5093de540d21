import React, { useState, useEffect } from 'react';
import RPGChat from './RPGChat';
import ChatSetup from './ChatSetup';
import ApiKeySetup from './ApiKeySetup';
import SystemPromptSettings from './SystemPromptSettings';
import PersonaCreation from './CharacterCreation';
import { Button } from './ui/button';
import { Settings, Plus, Zap, UserPlus, MessageSquare, Users, Globe, Clock } from 'lucide-react';

const Directory = () => {
  const [characters, setCharacters] = useState([]);
  const [worlds, setWorlds] = useState([]);
  const [recentChats, setRecentChats] = useState([]);
  const [stage, setStage] = useState('directory'); // 'directory', 'apiKey', 'systemPrompts', 'setup', 'character', 'chat'
  const [currentChat, setCurrentChat] = useState(null);
  const [chatSetupKey, setChatSetupKey] = useState(0); // Key to force ChatSetup re-render
  const [loading, setLoading] = useState(true);

  // Load characters, worlds, and recent chats
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Load characters
        const charactersResult = await window.electronAPI.getCharacterSettings();
        if (charactersResult.success) {
          setCharacters(charactersResult.settings);
        }
        
        // Load worlds
        const worldsResult = await window.electronAPI.getWorldSettings();
        if (worldsResult.success) {
          setWorlds(worldsResult.settings);
        }
        
        // Load recent chats
        const recentChatsResult = await window.electronAPI.getRecentChats(20);
        if (recentChatsResult.success) {
          setRecentChats(recentChatsResult.chats);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const reset = () => setStage('directory');

  const newChatFlow = async () => {
    const { success, apiKey } = await window.electronAPI.getActiveApiKey().catch(() => ({}));
    setStage(success && apiKey ? 'setup' : 'apiKey');
  };

  const openCharacterChat = async (character) => {
    try {
      // Get the most recent chat for this character
      const chatsResult = await window.electronAPI.getChats(character.id, 'character');
      let chatData = null;
      
      if (chatsResult.success && chatsResult.chats.length > 0) {
        // Use the most recent chat
        const latestChat = chatsResult.chats[chatsResult.chats.length - 1];
        chatData = JSON.parse(latestChat.contents);
      }
      
      // Set up the chat with character data
      const chatParams = {
        settingType: 'character',
        characterSetting: character,
        existingChat: chatData
      };
      
      setCurrentChat({ isNew: !chatData, params: chatParams });
      setStage('chat');
    } catch (error) {
      console.error('Error opening character chat:', error);
      // Fallback to new chat
      const chatParams = {
        settingType: 'character',
        characterSetting: character
      };
      setCurrentChat({ isNew: true, params: chatParams });
      setStage('chat');
    }
  };

  const openWorldChat = async (world) => {
    try {
      // Get the most recent chat for this world
      const chatsResult = await window.electronAPI.getChats(world.id, 'world');
      let chatData = null;
      
      if (chatsResult.success && chatsResult.chats.length > 0) {
        // Use the most recent chat
        const latestChat = chatsResult.chats[chatsResult.chats.length - 1];
        chatData = JSON.parse(latestChat.contents);
      }
      
      // Set up the chat with world data
      const chatParams = {
        settingType: 'world',
        worldSetting: world,
        existingChat: chatData
      };
      
      setCurrentChat({ isNew: !chatData, params: chatParams });
      setStage('chat');
    } catch (error) {
      console.error('Error opening world chat:', error);
      // Fallback to new chat
      const chatParams = {
        settingType: 'world',
        worldSetting: world
      };
      setCurrentChat({ isNew: true, params: chatParams });
      setStage('chat');
    }
  };

  // Render based on current stage
  if (stage === 'apiKey') return <ApiKeySetup onApiKeySet={() => setStage('setup')} onBack={reset} onShowSystemPrompts={() => setStage('systemPrompts')} />;
  if (stage === 'systemPrompts') return <SystemPromptSettings onBack={() => setStage('apiKey')} />;
  if (stage === 'character') return <PersonaCreation onBack={reset} onPersonaCreated={() => {
    // When a persona is created, go back to setup to show the updated persona list
    setChatSetupKey(prev => prev + 1); // Force ChatSetup to re-render and reload personas
    setStage('setup');
  }} />;
  if (stage === 'setup') return (
    <ChatSetup
      key={chatSetupKey}
      onStartChat={(params) => { setCurrentChat({ isNew: true, params }); setStage('chat'); }}
      onBack={reset}
      onNavigateToCharacterCreation={() => setStage('character')}
    />
  );
  if (stage === 'chat') return (
    <RPGChat
      parameters={currentChat.isNew ? currentChat.params : undefined}
      onBackToDirectory={reset}
    />
  );

  // Directory view
  return (
    <div className="min-h-screen bg-black text-white p-6">
      <header className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <img 
            src="/assets/Icon.png" 
            alt="Electrobox RPG Logo" 
            className="w-10 h-10 rounded-lg"
          />
          <h1 className="text-2xl font-bold text-blue-400">Electrobox RPG</h1>
        </div>
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('character')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <UserPlus className="w-4 h-4 mr-2" /> Create Character
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('apiKey')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <Settings className="w-4 h-4 mr-2" /> API Key
          </Button>
        </div>
      </header>

      <main className="max-w-6xl mx-auto">
        {/* New Chat Section */}
        <div className="mb-8 text-center">
          <Button 
            size="lg" 
            onClick={newChatFlow}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold"
          >
            <Plus className="w-5 h-5 mr-2" /> Start New Adventure
          </Button>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-gray-400 mt-4">Loading your adventures...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Characters Section */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5" /> Your Characters
              </h2>
              
              {characters.length > 0 ? (
                <div className="space-y-3">
                  {characters.map((character) => (
                    <div
                      key={character.id}
                      className="p-4 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                      onClick={() => openCharacterChat(character)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-white mb-1">{character.name}</h3>
                          <p className="text-sm text-gray-400 line-clamp-2 mb-2">
                            {character.personality}
                          </p>
                          <p className="text-xs text-gray-500">
                            Created: {new Date(character.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Button 
                          size="sm" 
                          className="bg-blue-600 hover:bg-blue-700 text-white ml-3"
                        >
                          Chat
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400 text-lg">No characters created yet.</p>
                  <p className="text-gray-500 text-sm mt-2">Create your first character to begin!</p>
                </div>
              )}
            </div>

            {/* Worlds Section */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
                <Globe className="w-5 h-5" /> Your Worlds
              </h2>
              
              {worlds.length > 0 ? (
                <div className="space-y-3">
                  {worlds.map((world) => (
                    <div
                      key={world.id}
                      className="p-4 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                      onClick={() => openWorldChat(world)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-white mb-1">{world.name}</h3>
                          <p className="text-sm text-gray-400 line-clamp-2 mb-2">
                            {world.world_description}
                          </p>
                          <p className="text-xs text-gray-500">
                            Created: {new Date(world.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Button 
                          size="sm" 
                          className="bg-blue-600 hover:bg-blue-700 text-white ml-3"
                        >
                          Chat
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400 text-lg">No worlds created yet.</p>
                  <p className="text-gray-500 text-sm mt-2">Create your first world to begin!</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Recent Chats Section */}
        {recentChats.length > 0 && (
          <div className="mt-8 bg-gray-900 rounded-lg p-6 border border-gray-800">
            <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" /> Recent Adventures
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {recentChats.slice(0, 6).map((chat, index) => (
                <div
                  key={`${chat.setting_id}-${chat.setting_type}-${index}`}
                  className="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                  onClick={() => {
                    if (chat.setting_type === 'character') {
                      const character = characters.find(c => c.id === chat.setting_id);
                      if (character) openCharacterChat(character);
                    } else if (chat.setting_type === 'world') {
                      const world = worlds.find(w => w.id === chat.setting_id);
                      if (world) openWorldChat(world);
                    }
                  }}
                >
                  <div className="flex items-center gap-2">
                    {chat.setting_type === 'character' ? (
                      <Users className="w-4 h-4 text-blue-400" />
                    ) : (
                      <Globe className="w-4 h-4 text-green-400" />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-white text-sm truncate">
                        {chat.setting_name || 'Unknown'}
                      </h3>
                      <p className="text-xs text-gray-400">
                        {new Date(chat.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Directory;
