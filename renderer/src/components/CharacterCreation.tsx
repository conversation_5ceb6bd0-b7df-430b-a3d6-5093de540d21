import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { ArrowLeft, Upload, Sparkles, Save, Wand2, UserPlus } from 'lucide-react';
import { cn } from '../lib/utils';
import ApiKeySetup from './ApiKeySetup';

interface PersonaCreationProps {
  onBack: () => void;
  onPersonaCreated?: () => void;
}

const PersonaCreation: React.FC<PersonaCreationProps> = ({ onBack, onPersonaCreated }) => {
  const [characterData, setCharacterData] = useState({
    name: '',
    personality: '',
    appearance: '',
    image: null as File | null,
    imagePreview: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancingField, setEnhancingField] = useState<'personality' | 'appearance' | null>(null);
  const [streamingText, setStreamingText] = useState('');
  const [showApiKeySetup, setShowApiKeySetup] = useState(false);
  const [pendingEnhanceField, setPendingEnhanceField] = useState<'personality' | 'appearance' | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cleanup event listener on unmount
  useEffect(() => {
    return () => {
      // Remove the event listener when component unmounts
      if (window.electronAPI && window.electronAPI.removeAllListeners) {
        window.electronAPI.removeAllListeners('enhance-character-text-chunk');
      }
    };
  }, []);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCharacterData(prev => ({
        ...prev,
        image: file,
        imagePreview: URL.createObjectURL(file)
      }));
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setCharacterData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const checkApiKeyAndEnhance = async (field: 'personality' | 'appearance') => {
    if (!characterData[field].trim()) {
      alert('Please enter some text first before enhancing.');
      return;
    }

    try {
      // Check if API key is set
      const { success, apiKey } = await window.electronAPI.getActiveApiKey();
      
      if (!success || !apiKey) {
        // No API key set, store the pending enhance request and show API key setup
        setPendingEnhanceField(field);
        setShowApiKeySetup(true);
        return;
      }

      // API key is set, proceed with enhancement
      await enhanceText(field);
    } catch (error) {
      console.error('Error checking API key:', error);
      alert('Error checking API key. Please try again.');
    }
  };

  const enhanceText = async (field: 'personality' | 'appearance') => {
    setIsEnhancing(true);
    setEnhancingField(field);
    setStreamingText('');

    try {
      // Convert image to base64 if exists
      let imageBase64 = '';
      if (characterData.image) {
        const reader = new FileReader();
        imageBase64 = await new Promise((resolve) => {
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(characterData.image!);
        });
      }

      // Set up streaming listener
      window.electronAPI.onEnhanceCharacterTextChunk((event, data) => {
        if (data.field === field) {
          setStreamingText(prev => prev + data.chunk);
        }
      });

      // Call the AI service to enhance the text with streaming
      const result = await window.electronAPI.enhanceCharacterTextStream({
        text: characterData[field],
        field: field,
        characterName: characterData.name || 'Character',
        imageBase64: imageBase64
      });

      if (result.success) {
        setCharacterData(prev => ({
          ...prev,
          [field]: result.enhancedText
        }));
      } else {
        alert('Failed to enhance text. Please try again.');
      }
    } catch (error) {
      console.error('Error enhancing text:', error);
      alert('Error enhancing text. Please try again.');
    } finally {
      setIsEnhancing(false);
      setEnhancingField(null);
      setStreamingText('');
    }
  };

  const handleApiKeySet = () => {
    setShowApiKeySetup(false);
    
    // If there was a pending enhance request, execute it now
    if (pendingEnhanceField) {
      enhanceText(pendingEnhanceField);
      setPendingEnhanceField(null);
    }
  };

  const handleApiKeySetupBack = () => {
    setShowApiKeySetup(false);
    setPendingEnhanceField(null);
  };

  const saveCharacter = async () => {
    if (!characterData.name.trim()) {
      alert('Please enter a character name.');
      return;
    }

    if (!characterData.personality.trim()) {
      alert('Please enter a personality description.');
      return;
    }

    if (!characterData.appearance.trim()) {
      alert('Please enter an appearance description.');
      return;
    }

    setIsLoading(true);
    try {
      // Convert image to base64 if exists
      let imageBase64 = '';
      if (characterData.image) {
        const reader = new FileReader();
        imageBase64 = await new Promise((resolve) => {
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(characterData.image!);
        });
      }

      const characterToSave = {
        id: Date.now().toString(),
        name: characterData.name,
        personality: characterData.personality,
        appearance: characterData.appearance,
        image: imageBase64,
        createdAt: new Date().toISOString()
      };

      // Save to database
      const result = await window.electronAPI.savePersona(characterToSave);
      
      if (result.success) {
        alert('Persona saved successfully!');
        if (onPersonaCreated) {
          onPersonaCreated();
        }
        onBack();
      } else {
        alert('Failed to save persona. Please try again.');
      }
    } catch (error) {
      console.error('Error saving character:', error);
      alert('Error saving character. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show API key setup if needed
  if (showApiKeySetup) {
    return (
      <ApiKeySetup 
        onApiKeySet={handleApiKeySet}
        onBack={handleApiKeySetupBack}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black flex flex-col relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-blue-500/30 relative z-10">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBack}
            className="text-blue-300 hover:text-white hover:bg-blue-500/20 transition-all duration-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-white flex items-center gap-2">
              <UserPlus className="w-6 h-6 text-blue-400" />
              Create Your Persona
            </h1>
            <p className="text-sm text-blue-300">Design the persona you'll play as in your adventures</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-8 relative z-10">
        <div className="w-full max-w-4xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Image Upload */}
            <Card className="bg-blue-500/10 backdrop-blur-sm border border-blue-500/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Upload className="w-5 h-5 text-blue-400" />
                  Character Image
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed border-blue-500/50 rounded-xl hover:border-blue-400 transition-colors cursor-pointer"
                     onClick={() => fileInputRef.current?.click()}>
                  {characterData.imagePreview ? (
                    <div className="relative">
                      <img 
                        src={characterData.imagePreview} 
                        alt="Character preview" 
                        className="w-48 h-48 object-cover rounded-xl shadow-lg"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-2 right-2 bg-black/50 border-blue-500/50 text-blue-300 hover:bg-blue-500/20"
                        onClick={(e) => {
                          e.stopPropagation();
                          setCharacterData(prev => ({ ...prev, image: null, imagePreview: '' }));
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 mx-auto bg-blue-500/20 rounded-full flex items-center justify-center">
                        <Upload className="w-8 h-8 text-blue-400" />
                      </div>
                      <div>
                        <p className="text-blue-200 font-medium">Click to upload image</p>
                        <p className="text-blue-300/70 text-sm">PNG, JPG up to 5MB</p>
                      </div>
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </CardContent>
            </Card>

            {/* Right Column - Character Details */}
            <div className="space-y-6">
              {/* Name */}
              <Card className="bg-blue-500/10 backdrop-blur-sm border border-blue-500/30">
                <CardHeader>
                  <CardTitle className="text-white">Persona Name</CardTitle>
                </CardHeader>
                <CardContent>
                  <Input
                    placeholder="Enter your persona's name..."
                    value={characterData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="bg-blue-500/5 border-blue-500/30 text-white placeholder:text-blue-300/50 focus:border-blue-400"
                  />
                </CardContent>
              </Card>

              {/* Personality */}
              <Card className="bg-blue-500/10 backdrop-blur-sm border border-blue-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <span>Personality</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => checkApiKeyAndEnhance('personality')}
                      disabled={isEnhancing}
                      className="border-blue-500/50 text-blue-300 hover:bg-blue-500/20"
                    >
                      <Wand2 className="w-4 h-4 mr-2" />
                      {isEnhancing ? 'Streaming...' : `Enhance${characterData.image ? '' : ''}`}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Describe your persona's personality, traits, quirks, and behavior..."
                    value={enhancingField === 'personality' ? streamingText : characterData.personality}
                    onChange={(e) => handleInputChange('personality', e.target.value)}
                    rows={4}
                    className={`bg-blue-500/5 border-blue-500/30 text-white placeholder:text-blue-300/50 focus:border-blue-400 resize-none ${
                      enhancingField === 'personality' ? 'animate-pulse' : ''
                    }`}
                    disabled={enhancingField === 'personality'}
                  />
                </CardContent>
              </Card>

              {/* Appearance */}
              <Card className="bg-blue-500/10 backdrop-blur-sm border border-blue-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <span>Appearance</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => checkApiKeyAndEnhance('appearance')}
                      disabled={isEnhancing}
                      className="border-blue-500/50 text-blue-300 hover:bg-blue-500/20"
                    >
                      <Wand2 className="w-4 h-4 mr-2" />
                      {isEnhancing ? 'Streaming...' : `Enhance${characterData.image ? '' : ''}`}
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Describe your persona's physical appearance, clothing, and distinguishing features..."
                    value={enhancingField === 'appearance' ? streamingText : characterData.appearance}
                    onChange={(e) => handleInputChange('appearance', e.target.value)}
                    rows={4}
                    className={`bg-blue-500/5 border-blue-500/30 text-white placeholder:text-blue-300/50 focus:border-blue-400 resize-none ${
                      enhancingField === 'appearance' ? 'animate-pulse' : ''
                    }`}
                    disabled={enhancingField === 'appearance'}
                  />
                </CardContent>
              </Card>

              {/* Save Button */}
              <Button
                onClick={saveCharacter}
                disabled={isLoading || !characterData.name.trim()}
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-black py-4 text-lg font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <Save className="w-5 h-5 mr-2" />
                {isLoading ? 'Saving...' : 'Save Persona'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonaCreation; 