import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Textarea } from './ui/textarea';
import { ArrowLeft, Upload, Sparkles, User, Globe, Plus, UserPlus } from 'lucide-react';
import type { ElectronAPI } from '../types/electron.d';

// New interfaces for the two-tier system
export interface CharacterSetting {
  id?: number;
  name: string;
  character: string; // User's role/relationship to the AI character
  personality: string;
  appearance: string; // AI character's appearance
  scenario: string;
  initialMessage: string;
  exampleDialogs: string;
  imageUrl?: string;
}

export interface WorldSetting {
  id?: number;
  name: string;
  worldDescription: string;
  characterDescription: string;
  characterRole: string;
}

// Interface for user personas (personas the user roleplays as)
export interface UserPersona {
  id: string;
  name: string;
  personality: string;
  appearance: string;
  image?: string;
  createdAt: string;
}

export interface ChatParameters {
  settingType: 'character' | 'world';
  characterSetting?: CharacterSetting;
  worldSetting?: WorldSetting;
  userPersona?: UserPersona; // Add user persona to chat parameters
}

interface ChatSetupProps {
  onStartChat: (parameters: ChatParameters) => void;
  onBack: () => void;
  onNavigateToCharacterCreation?: () => void;
}

const ChatSetup: React.FC<ChatSetupProps> = ({ onStartChat, onBack, onNavigateToCharacterCreation }) => {
  const [setupType, setSetupType] = useState<'selection' | 'character' | 'world' | 'character-selection'>('selection');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userPersonas, setUserPersonas] = useState<UserPersona[]>([]);
  const [selectedUserPersona, setSelectedUserPersona] = useState<UserPersona | null>(null);
  const [showCharacterCreation, setShowCharacterCreation] = useState(false);

  // Character creation state
  const [characterSetting, setCharacterSetting] = useState<CharacterSetting>({
    name: '',
    character: '', // User's role/relationship to the AI character
    personality: '',
    appearance: '', // AI character's appearance
    scenario: '',
    initialMessage: '',
    exampleDialogs: '',
    imageUrl: ''
  });

  // World creation state
  const [worldSetting, setWorldSetting] = useState<WorldSetting>({
    name: '',
    worldDescription: '',
    characterDescription: '',
    characterRole: ''
  });

  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [streamingField, setStreamingField] = useState<string | null>(null);
  const [streamedValue, setStreamedValue] = useState<string>('');

  // Load user personas on component mount
  useEffect(() => {
    loadUserPersonas();
  }, []);



  const loadUserPersonas = async () => {
    try {
      const result = await window.electronAPI.getPersonas();
      if (result.success) {
        setUserPersonas(result.personas || []);
      }
    } catch (error) {
      console.error('Error loading user personas:', error);
    }
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // AI Spark functionality (now streaming)
  const handleAISpark = async (field: string, currentValue: string) => {
    if (!hasEnoughContext()) {
      setError('Not enough context. Please fill in some fields first.');
      return;
    }

    setIsLoading(true);
    setError('');
    setStreamingField(field);
    setStreamedValue('');

    try {
      const context = buildContextForAI();
      const prompt = generatePromptForField(field, context);

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${await getOpenRouterApiKey()}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "Electrobox Chat Setup",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "model": "deepseek/deepseek-chat-v3-0324",
          "messages": [
            {
              "role": "user",
              "content": prompt
            }
          ],
          "stream": true
        })
      });

      if (!response.ok || !response.body) {
        throw new Error('Failed to get AI response');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let done = false;
      let fullText = '';
      let buffer = '';

      while (!done) {
        const { value, done: doneReading } = await reader.read();
        done = doneReading;
        if (value) {
          buffer += decoder.decode(value, { stream: !done });
          // Split on newlines for SSE format
          let lines = buffer.split('\n');
          buffer = lines.pop() || '';
          for (const line of lines) {
            if (line.startsWith('data:')) {
              const dataStr = line.replace('data:', '').trim();
              if (dataStr === '[DONE]') continue;
              try {
                const data = JSON.parse(dataStr);
                const delta = data.choices?.[0]?.delta?.content || '';
                if (delta) {
                  fullText += delta;
                  setStreamedValue(fullText);
                }
              } catch (e) {
                // ignore malformed lines
              }
            }
          }
        }
      }
      // Final update
      updateFieldWithAIResponse(field, fullText);
      setStreamingField(null);
      setStreamedValue('');
    } catch (err) {
      setError('Failed to generate AI content. Please try again.');
      setStreamingField(null);
      setStreamedValue('');
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions
  const hasEnoughContext = () => {
    if (setupType === 'character') {
      return characterSetting.name || characterSetting.character || characterSetting.personality || characterSetting.appearance;
    } else {
      return worldSetting.name || worldSetting.worldDescription || worldSetting.characterDescription;
    }
  };

  const buildContextForAI = () => {
    if (setupType === 'character') {
      return {
        aiCharacterName: characterSetting.name,
        userRoleInRelationToAICharacter: characterSetting.character,
        aiCharacterPersonality: characterSetting.personality,
        aiCharacterAppearance: characterSetting.appearance,
        conversationScenario: characterSetting.scenario,
        userPersona: selectedUserPersona ? {
          name: selectedUserPersona.name,
          personality: selectedUserPersona.personality,
          appearance: selectedUserPersona.appearance
        } : null
      };
    } else {
      return {
        worldName: worldSetting.name,
        worldDescription: worldSetting.worldDescription,
        userCharacterDescription: worldSetting.characterDescription,
        userCharacterRole: worldSetting.characterRole,
        userPersona: selectedUserPersona ? {
          name: selectedUserPersona.name,
          personality: selectedUserPersona.personality,
          appearance: selectedUserPersona.appearance
        } : null
      };
    }
  };

  const generatePromptForField = (field: string, context: any) => {
    const contextStr = Object.entries(context)
      .filter(([_, value]) => value && _ !== 'userPersona')
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');

    const userPersonaStr = context.userPersona ? 
      `\n\nUSER PERSONA (who the user is roleplaying as):\nName: ${context.userPersona.name}\nPersonality: ${context.userPersona.personality}\nAppearance: ${context.userPersona.appearance}` : '';

    const prompts = {
      character: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDefine the user's role, relationship, or identity in relation to the AI character being created. Who is the user to this AI character? What is their relationship or position? Consider the user's persona when defining this relationship.`,
      personality: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the AI character's personality, mannerisms, speech patterns, and behavioral traits. This is for the AI character being created, NOT the user's persona. Make it engaging and specific.`,
      appearance: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the AI character's physical appearance, clothing, style, and visual characteristics. This is for the AI character being created, NOT the user's persona. Be detailed and vivid in describing their looks, attire, and any distinctive visual features.`,
      scenario: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the current circumstances and context of the conversation between the user (with their persona) and the AI character being created. Set the scene and situation.`,
      initialMessage: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nWrite a lengthy, engaging first message from the AI character being created. This message should be directed to the user (who is roleplaying as their persona). Make it immersive and encourage detailed responses.`,
      exampleDialogs: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nCreate example dialog exchanges using {{char}} (for the AI character being created) and {{user}} (for the user with their persona) format. Show the AI character's personality through conversation.`,
      worldDescription: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nCreate a rich, detailed world description including setting, atmosphere, key locations, and important background information.`,
      characterDescription: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nDescribe the user's character in this world - their appearance, background, and current situation. This refers to the user's persona in this world setting.`,
      characterRole: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nDefine the user character's role, position, or significance within this world setting. This refers to the user's persona's role in this world.`
    };

    return prompts[field as keyof typeof prompts] || `Generate content for ${field} based on: ${contextStr}${userPersonaStr}`;
  };

  const updateFieldWithAIResponse = (field: string, response: string) => {
    if (setupType === 'character') {
      setCharacterSetting(prev => ({
        ...prev,
        [field]: response
      }));
    } else {
      setWorldSetting(prev => ({
        ...prev,
        [field]: response
      }));
    }
  };

  // Generate AI options for chat interactions
  const generateAIOptions = async (settingType: 'character' | 'world', setting: any): Promise<string[]> => {
    try {
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        // Return default options if no API key
        return [
          'Continue the conversation',
          'Ask a question',
          'Take action',
          'Observe the situation'
        ];
      }

      let prompt = '';
      if (settingType === 'character') {
        prompt = `Based on this AI character setup:
AI Character Name: ${setting.name}
User's Role in Relation to AI Character: ${setting.character}
AI Character's Personality: ${setting.personality}
AI Character's Appearance: ${setting.appearance}
Conversation Scenario: ${setting.scenario}
AI Character's Initial Message: ${setting.initialMessage}

IMPORTANT: You are generating response options for the USER to choose from when interacting with this AI character. The user has their own persona they roleplay as.

Generate exactly 4 contextual response options that the user could choose from. Each option should be 2-6 words and reflect different ways the user might respond or act when interacting with this AI character.

Respond with a JSON object in this format:
{
  "options": [
    "option 1 text",
    "option 2 text",
    "option 3 text",
    "option 4 text"
  ]
}`;
      } else {
        prompt = `Based on this world setup:
World Name: ${setting.name}
World Description: ${setting.worldDescription}
User's Character Description: ${setting.characterDescription}
User's Character Role: ${setting.characterRole}

IMPORTANT: You are generating response options for the USER to choose from when interacting in this world. The user has their own persona they roleplay as.

Generate exactly 4 contextual response options that the user could choose from. Each option should be 2-6 words and reflect different ways the user might respond or act in this world.

Respond with a JSON object in this format:
{
  "options": [
    "option 1 text",
    "option 2 text",
    "option 3 text",
    "option 4 text"
  ]
}`;
      }

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 150,
          temperature: 0.7,
          response_format: { type: 'json_object' }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const generatedContent = data.choices?.[0]?.message?.content?.trim();

      if (generatedContent) {
        try {
          // Parse the JSON response
          const responseData = JSON.parse(generatedContent);
          
          if (responseData.options && Array.isArray(responseData.options)) {
            const options = responseData.options.slice(0, 4);
            
            if (options.length === 4) {
              return options;
            }
          }
        } catch (e) {
          console.error('Failed to parse JSON options:', e);
          console.log('Raw response:', generatedContent);
        }
      }
    } catch (err) {
      console.error('Failed to generate AI options:', err);
    }

    // Fallback options
    return settingType === 'character'
      ? [
          'Act according to personality',
          'Engage with the scenario',
          'Explore surroundings',
          'Reflect on situation'
        ]
      : [
          'Embrace your role',
          'Explore the world',
          'Seek interaction',
          'Adapt to circumstances'
        ];
  };

  const getOpenRouterApiKey = async () => {
    try {
      const result = await window.electronAPI.getActiveApiKey();
      if (result.success && result.apiKey) {
        return result.apiKey.api_key;
      }
      throw new Error('No API key found');
    } catch (err) {
      throw new Error('Failed to get API key');
    }
  };

  // Handle persona selection
  const handlePersonaSelection = (persona: UserPersona) => {
    setSelectedUserPersona(persona);
    // Go back to the previous screen (either selection or character/world creation)
    if (setupType === 'character-selection') {
      setSetupType('selection');
    }
  };

  // Handle creating new character
  const handleCreateNewCharacter = () => {
    if (onNavigateToCharacterCreation) {
      onNavigateToCharacterCreation();
    } else {
      setShowCharacterCreation(true);
    }
  };

  // Form submission handlers
  const handleCharacterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!characterSetting.name || !characterSetting.character || !characterSetting.personality ||
        !characterSetting.appearance || !characterSetting.scenario || !characterSetting.initialMessage || !characterSetting.exampleDialogs) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure user has selected a persona to roleplay as
    if (!selectedUserPersona) {
      setError('Please select a persona to roleplay as');
      return;
    }

    setIsLoading(true);
    try {
      // Save character setting to database
      const settingToSave = {
        ...characterSetting,
        imageUrl: imagePreview
      };

      const result = await window.electronAPI.saveCharacterSetting(settingToSave);

      if (result.success) {
        // Generate AI options for the initial message
        const aiOptions = await generateAIOptions('character', settingToSave);

        // Create initial chat entry with AI-generated options
        const chatContent = {
          content: characterSetting.initialMessage,
          options: aiOptions,
          userPersona: selectedUserPersona // Include user persona in chat
        };

        const initialChatResult = await window.electronAPI.saveChat(
          null, // no parent
          result.id,
          'character',
          JSON.stringify(chatContent)
        );

        if (initialChatResult.success) {
          const parameters: ChatParameters = {
            settingType: 'character',
            characterSetting: {
              ...settingToSave,
              id: result.id
            },
            userPersona: selectedUserPersona
          };

          onStartChat(parameters);
        } else {
          setError('Failed to create initial chat message');
        }
      } else {
        setError(result.error || 'Failed to save character setting');
      }
    } catch (err) {
      setError('Failed to create character. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWorldSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!worldSetting.name || !worldSetting.worldDescription ||
        !worldSetting.characterDescription || !worldSetting.characterRole) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure user has selected a persona to roleplay as
    if (!selectedUserPersona) {
      setError('Please select a persona to roleplay as');
      return;
    }

    setIsLoading(true);
    try {
      // Save world setting to database
      const result = await window.electronAPI.saveWorldSetting(worldSetting);

      if (result.success) {
        // Generate AI options for the world setting
        const aiOptions = await generateAIOptions('world', worldSetting);

        // Create initial chat entry with a generated opening message and AI options
        const initialMessage = `Welcome to ${worldSetting.name}. ${worldSetting.worldDescription}`;
        const chatContent = {
          content: initialMessage,
          options: aiOptions,
          userPersona: selectedUserPersona // Include user persona in chat
        };

        const initialChatResult = await window.electronAPI.saveChat(
          null, // no parent
          result.id,
          'world',
          JSON.stringify(chatContent)
        );

        if (initialChatResult.success) {
          const parameters: ChatParameters = {
            settingType: 'world',
            worldSetting: {
              ...worldSetting,
              id: result.id
            },
            userPersona: selectedUserPersona
          };

          onStartChat(parameters);
        } else {
          setError('Failed to create initial chat message');
        }
      } else {
        setError(result.error || 'Failed to save world setting');
      }
    } catch (err) {
      setError('Failed to create world. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render character selection
  const renderCharacterSelection = () => (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => {
            // If we have a selected persona, go back to the creation screen
            if (selectedUserPersona) {
              setSetupType('selection');
            } else {
              onBack();
            }
          }} 
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Choose Your Persona</h1>
      </div>

      <div className="mb-6">
        <p className="text-muted-foreground mb-4">
          Select a persona to roleplay as, or create a new one.
        </p>
        
        {selectedUserPersona && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800 font-medium">
              Selected: <span className="font-bold">{selectedUserPersona.name}</span>
            </p>
            <p className="text-green-700 text-sm mt-1">{selectedUserPersona.personality}</p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {userPersonas.map((persona) => (
          <Card 
            key={persona.id} 
            className={`cursor-pointer hover:shadow-lg transition-shadow ${
              selectedUserPersona?.id === persona.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
            }`}
            onClick={() => handlePersonaSelection(persona)}
          >
                          <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  {persona.image ? (
                    <img 
                      src={persona.image} 
                      alt={persona.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="w-6 h-6 text-gray-500" />
                    </div>
                  )}
                  <div>
                    <CardTitle className="text-lg">{persona.name}</CardTitle>
                    <CardDescription className="text-sm">
                      Created {new Date(persona.createdAt).toLocaleDateString()}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {persona.personality}
                </p>
              </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-center">
                  <Button 
            onClick={handleCreateNewCharacter}
            className="flex items-center space-x-2"
          >
            <UserPlus className="w-4 h-4" />
            <span>Create New Persona</span>
          </Button>
      </div>
    </div>
  );

  // Render setup type selection
  const renderSetupSelection = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create New Chat</h1>
      </div>

      {selectedUserPersona && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 font-medium">
            Roleplaying as: <span className="font-bold">{selectedUserPersona.name}</span>
          </p>
          <p className="text-blue-700 text-sm mt-1">{selectedUserPersona.personality}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setSetupType('character-selection')}
            className="mt-2"
          >
            Change Persona
          </Button>
        </div>
      )}

      {!selectedUserPersona && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            Please select a persona to roleplay as first.
          </p>
          <Button 
            onClick={() => setSetupType('character-selection')}
            className="mt-2"
          >
            Choose Persona
          </Button>
        </div>
      )}

      <div className="space-y-6">
        <Card 
          className={`cursor-pointer hover:shadow-lg transition-shadow ${
            !selectedUserPersona ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={() => selectedUserPersona && setSetupType('character')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <User className="h-6 w-6" />
              Create a Character
            </CardTitle>
            <CardDescription>
              Design a character with personality, backstory, and conversation style for character-based roleplay
            </CardDescription>
          </CardHeader>
        </Card>

        <Card 
          className={`cursor-pointer hover:shadow-lg transition-shadow ${
            !selectedUserPersona ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={() => selectedUserPersona && setSetupType('world')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Globe className="h-6 w-6" />
              Create a World
            </CardTitle>
            <CardDescription>
              Build a world or scenario with detailed setting and define your character's role within it
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );

  // AI Spark button component
  const AISparkButton = ({ field, disabled }: { field: string; disabled?: boolean }) => (
    <Button
      type="button"
      variant="outline"
      size="sm"
      onClick={() => handleAISpark(field, '')}
      disabled={disabled || isLoading || streamingField === field}
      className="ml-2"
    >
      <Sparkles className="h-4 w-4" />
      {streamingField === field && (
        <span className="ml-1 animate-pulse">...</span>
      )}
    </Button>
  );

  // Render character creation form
  const renderCharacterCreation = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={() => setSetupType('selection')} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create a Character</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Current Persona Display */}
      {selectedUserPersona && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Your Current Persona</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSetupType('character-selection')}
              >
                Change Persona
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              {selectedUserPersona.image ? (
                <img 
                  src={selectedUserPersona.image} 
                  alt={selectedUserPersona.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-300">
                  <User className="w-8 h-8 text-gray-500" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="font-semibold text-lg">{selectedUserPersona.name}</h3>
                <p className="text-sm text-muted-foreground mb-2">{selectedUserPersona.personality}</p>
                {selectedUserPersona.appearance && (
                  <p className="text-xs text-muted-foreground">{selectedUserPersona.appearance}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleCharacterSubmit} className="space-y-6">
        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle>Character Avatar</CardTitle>
            <CardDescription>Upload an image for your character</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="flex-1"
                />
                <Upload className="h-5 w-5 text-muted-foreground" />
              </div>
              {imagePreview && (
                <div className="mt-4">
                  <img
                    src={imagePreview}
                    alt="Character preview"
                    className="w-32 h-32 object-cover rounded-lg border"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Character Name */}
        <Card>
          <CardHeader>
            <CardTitle>Character Name</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Input
                value={streamingField === 'name' ? streamedValue : characterSetting.name}
                onChange={(e) => setCharacterSetting(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter character's name"
                required
                className="flex-1"
                disabled={streamingField === 'name'}
              />
              <AISparkButton field="name" />
            </div>
          </CardContent>
        </Card>

        {/* User's Role/Persona */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Your Role/Persona
              <AISparkButton field="character" />
            </CardTitle>
            <CardDescription>Who you are to this character - your relationship, role, or identity in their world</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'character' ? streamedValue : characterSetting.character}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, character: e.target.value }))}
              placeholder="Describe who you are to this character - your relationship, role, identity, or how they should see you..."
              rows={4}
              required
              disabled={streamingField === 'character'}
            />
          </CardContent>
        </Card>

        {/* Personality */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Personality
              <AISparkButton field="personality" />
            </CardTitle>
            <CardDescription>Describe the character's persona here</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'personality' ? streamedValue : characterSetting.personality}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, personality: e.target.value }))}
              placeholder="Describe personality traits, mannerisms, speech patterns..."
              rows={4}
              required
              disabled={streamingField === 'personality'}
            />
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Appearance
              <AISparkButton field="appearance" />
            </CardTitle>
            <CardDescription>Describe the AI character's physical appearance, clothing, and visual characteristics</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'appearance' ? streamedValue : characterSetting.appearance}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, appearance: e.target.value }))}
              placeholder="Describe the character's physical appearance, clothing, style, and visual traits..."
              rows={4}
              required
              disabled={streamingField === 'appearance'}
            />
          </CardContent>
        </Card>

        {/* Scenario */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Scenario
              <AISparkButton field="scenario" />
            </CardTitle>
            <CardDescription>The current circumstances and context of the conversation and the characters</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'scenario' ? streamedValue : characterSetting.scenario}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, scenario: e.target.value }))}
              placeholder="Set the scene and current situation..."
              rows={4}
              required
              disabled={streamingField === 'scenario'}
            />
          </CardContent>
        </Card>

        {/* Initial Message */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Initial Message
              <AISparkButton field="initialMessage" />
            </CardTitle>
            <CardDescription>First message from your character. Provide a lengthy first message to encourage the character to give longer responses</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'initialMessage' ? streamedValue : characterSetting.initialMessage}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, initialMessage: e.target.value }))}
              placeholder="Write the character's opening message..."
              rows={6}
              required
              disabled={streamingField === 'initialMessage'}
            />
          </CardContent>
        </Card>

        {/* Example Dialogs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Example Dialogs
              <AISparkButton field="exampleDialogs" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'exampleDialogs' ? streamedValue : characterSetting.exampleDialogs}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, exampleDialogs: e.target.value }))}
              placeholder={`{{char}}: Hey, im Mark\n{{user}}: hello Mark\n{{char}}: nice to meet you :)`}
              rows={6}
              required
              disabled={streamingField === 'exampleDialogs'}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => setSetupType('selection')}>
            Back
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Start Chat'}
          </Button>
        </div>
      </form>
    </div>
  );

  // Render world creation form
  const renderWorldCreation = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={() => setSetupType('selection')} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create a World</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Current Persona Display */}
      {selectedUserPersona && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Your Current Persona</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSetupType('character-selection')}
              >
                Change Persona
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              {selectedUserPersona.image ? (
                <img 
                  src={selectedUserPersona.image} 
                  alt={selectedUserPersona.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-300">
                  <User className="w-8 h-8 text-gray-500" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="font-semibold text-lg">{selectedUserPersona.name}</h3>
                <p className="text-sm text-muted-foreground mb-2">{selectedUserPersona.personality}</p>
                {selectedUserPersona.appearance && (
                  <p className="text-xs text-muted-foreground">{selectedUserPersona.appearance}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleWorldSubmit} className="space-y-6">
        {/* World Name */}
        <Card>
          <CardHeader>
            <CardTitle>World Name</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Input
                value={streamingField === 'name' ? streamedValue : worldSetting.name}
                onChange={(e) => setWorldSetting(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter world/scenario name"
                required
                className="flex-1"
                disabled={streamingField === 'name'}
              />
              <AISparkButton field="name" />
            </div>
          </CardContent>
        </Card>

        {/* World Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              World Description
              <AISparkButton field="worldDescription" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'worldDescription' ? streamedValue : worldSetting.worldDescription}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, worldDescription: e.target.value }))}
              placeholder="Describe the world, setting, atmosphere, key locations, and background information..."
              rows={6}
              required
              disabled={streamingField === 'worldDescription'}
            />
          </CardContent>
        </Card>

        {/* Character Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Your Character Description
              <AISparkButton field="characterDescription" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'characterDescription' ? streamedValue : worldSetting.characterDescription}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, characterDescription: e.target.value }))}
              placeholder="Describe your character's appearance, background, and current situation in this world..."
              rows={4}
              required
              disabled={streamingField === 'characterDescription'}
            />
          </CardContent>
        </Card>

        {/* Character Role */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Character Role
              <AISparkButton field="characterRole" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={streamingField === 'characterRole' ? streamedValue : worldSetting.characterRole}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, characterRole: e.target.value }))}
              placeholder="Define your character's role, position, or significance within this world..."
              rows={3}
              required
              disabled={streamingField === 'characterRole'}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => setSetupType('selection')}>
            Back
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Start Chat'}
          </Button>
        </div>
      </form>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      {setupType === 'selection' && renderSetupSelection()}
      {setupType === 'character-selection' && renderCharacterSelection()}
      {setupType === 'character' && renderCharacterCreation()}
      {setupType === 'world' && renderWorldCreation()}
    </div>
  );
};

export default ChatSetup; 