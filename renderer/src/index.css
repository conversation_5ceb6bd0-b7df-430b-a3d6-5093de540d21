@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* RPG Theme - Rich fantasy colors */
    --background: 220 15% 8%;
    --foreground: 45 100% 95%;

    --card: 220 20% 12%;
    --card-foreground: 45 100% 95%;

    --popover: 220 25% 10%;
    --popover-foreground: 45 100% 95%;

    --primary: 280 100% 65%;
    --primary-foreground: 220 15% 8%;
    --primary-glow: 280 100% 80%;

    --secondary: 200 100% 40%;
    --secondary-foreground: 45 100% 95%;

    --muted: 220 20% 18%;
    --muted-foreground: 45 20% 65%;

    --accent: 45 100% 60%;
    --accent-foreground: 220 15% 8%;

    --destructive: 0 85% 55%;
    --destructive-foreground: 45 100% 95%;

    --success: 120 60% 50%;
    --success-foreground: 45 100% 95%;

    --border: 220 20% 20%;
    --input: 220 20% 15%;
    --ring: 280 100% 65%;

    /* RPG-specific colors */
    --character-user: 280 100% 65%;
    --character-ai: 200 100% 50%;
    --choice-hover: 45 100% 60%;
    --stat-positive: 120 60% 50%;
    --stat-negative: 0 85% 55%;
    --inventory-rare: 280 100% 65%;
    --inventory-common: 45 20% 65%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(280 100% 65%), hsl(280 100% 80%));
    --gradient-secondary: linear-gradient(135deg, hsl(200 100% 40%), hsl(200 100% 60%));
    --gradient-accent: linear-gradient(135deg, hsl(45 100% 60%), hsl(45 100% 80%));
    --gradient-background: linear-gradient(180deg, hsl(220 15% 8%), hsl(220 20% 12%));
    --gradient-card: linear-gradient(145deg, hsl(220 20% 12%), hsl(220 25% 15%));

    /* Shadows */
    --shadow-primary: 0 10px 30px -10px hsl(280 100% 65% / 0.3);
    --shadow-accent: 0 5px 20px -5px hsl(45 100% 60% / 0.4);
    --shadow-character: 0 8px 25px -8px hsl(280 100% 65% / 0.6);
    --shadow-glow: 0 0 40px hsl(280 100% 80% / 0.4);

    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
  }
}